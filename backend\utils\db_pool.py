"""
数据库连接池模块，用于管理PostgreSQL数据库连接。
提供异步连接池功能，适用于asyncio应用。
"""

import asyncpg
import asyncio
from contextlib import asynccontextmanager
from typing import Optional
from logger import logger
from const import PGSQL_CONFIG

# 全局异步连接池实例
_pool: Optional[asyncpg.Pool] = None
_pool_lock = asyncio.Lock()
_pool_initialized = False

# 连接池配置
# 延迟 PGSQL_CONFIG 的使用，直到 initialize_pool 确保它已加载且有效
MIN_CONNECTIONS_DEFAULT = 2
MAX_CONNECTIONS_DEFAULT = 10

async def initialize_pool():
    """初始化异步数据库连接池"""
    global _pool, _pool_initialized, MIN_CONNECTIONS, MAX_CONNECTIONS
    if _pool_initialized:
        logger.debug("Pool already initialized.")
        return

    async with _pool_lock:
        if _pool_initialized:  # 双重检查锁定模式
            logger.debug("Pool already initialized (after lock).")
            return

        logger.info("Attempting to initialize asynchronous database connection pool...")

        if not isinstance(PGSQL_CONFIG, dict):
            logger.error(f"PGSQL_CONFIG is not a dictionary or not defined correctly. Type: {type(PGSQL_CONFIG)}. Value: {PGSQL_CONFIG}")
            _pool_initialized = False
            _pool = None
            raise TypeError("PGSQL_CONFIG must be a dictionary for database connection.")

        # 从配置中获取连接数，如果未提供则使用默认值
        min_connections = PGSQL_CONFIG.get("min_size", MIN_CONNECTIONS_DEFAULT)
        max_connections = PGSQL_CONFIG.get("max_size", MAX_CONNECTIONS_DEFAULT)

        # 创建一个用于日志记录的配置副本，隐藏密码
        logged_config = PGSQL_CONFIG.copy()
        if 'password' in logged_config:
            logged_config['password'] = '********'
        logger.info(f"Using PGSQL_CONFIG: {logged_config} (min_size={min_connections}, max_size={max_connections})")

        try:
            _pool = await asyncpg.create_pool(
                **PGSQL_CONFIG,  # 解包用户提供的配置
                min_size=min_connections,
                max_size=max_connections
            )
            _pool_initialized = True
            logger.info("Asynchronous database connection pool initialized successfully.")
        except TypeError as te:
            logger.error(f"TypeError during pool creation. PGSQL_CONFIG might be malformed or missing required arguments for asyncpg.create_pool: {te}. Config was: {logged_config}")
            _pool_initialized = False
            _pool = None
            raise
        except (asyncpg.exceptions.InvalidConnectionParametersError, asyncpg.exceptions.CannotConnectNowError) as conn_err:
            logger.error(f"Connection error during asyncpg.create_pool: {conn_err.__class__.__name__}: {conn_err}. Config was: {logged_config}")
            _pool_initialized = False
            _pool = None
            raise
        except Exception as e:
            logger.error(f"Failed to initialize asynchronous database connection pool with an unexpected error: {e.__class__.__name__}: {e}. Config was: {logged_config}")
            _pool_initialized = False
            _pool = None
            raise

async def shutdown_pool():
    """关闭异步数据库连接池"""
    global _pool, _pool_initialized
    if _pool_initialized:
        async with _pool_lock:
            if _pool_initialized and _pool:
                logger.info("Closing asynchronous database connection pool.")
                await _pool.close()
                _pool = None
                _pool_initialized = False
                logger.info("Asynchronous database connection pool closed.")

@asynccontextmanager
async def get_connection():
    """
    从异步连接池获取连接的异步上下文管理器。
    使用示例:
    ```
    async with get_connection() as conn:
        # conn 是一个 asyncpg.Connection 对象
        await conn.execute("SELECT * FROM table")
    ```
    """
    if not _pool_initialized or _pool is None:
        logger.info("Pool not initialized or None in get_connection. Attempting to (re)initialize.")
        try:
            await initialize_pool()
        except Exception as e:
            # 此处的日志记录了在 get_connection 内部尝试初始化失败的情况
            logger.error(f"Error during lazy initialization of pool in get_connection: {e.__class__.__name__}: {e}")
            # 重新抛出异常，这将导致 __aenter__ 失败
            raise RuntimeError(f"Failed to initialize database pool during get_connection: {e}")

    if _pool is None: # 双重检查，确保 initialize_pool 成功设置了 _pool
        logger.error("Database pool is None even after initialization attempt in get_connection.")
        raise RuntimeError("Database pool is not available (None after init attempt).")

    conn = None  # 初始化 conn 为 None，以防 acquire 失败
    try:
        logger.debug("Acquiring connection from pool...")
        conn = await _pool.acquire()
        logger.debug("Connection acquired successfully.")
        yield conn
    except asyncio.TimeoutError: # 如果 acquire 设置了超时并且超时
        logger.error("Timeout acquiring database connection from pool.")
        raise RuntimeError("Timeout acquiring database connection.")
    except Exception as e:
        # 此处的日志记录了从已初始化的池中获取连接失败的情况
        logger.error(f"Error acquiring database connection from pool: {e.__class__.__name__}: {e}")
        # 重新抛出异常
        raise RuntimeError(f"Failed to acquire database connection: {e}")
    finally:
        if conn is not None: # 确保 conn 不是 None 才尝试释放
            try:
                logger.debug("Releasing connection back to pool.")
                await _pool.release(conn)
                logger.debug("Connection released successfully.")
            except Exception as e:
                logger.error(f"Error releasing database connection: {e.__class__.__name__}: {e}")
