
import asyncpg
from datetime import datetime, timedelta
from typing import Optional, Tuple, List

from utils.db_pool import get_connection
from logger import logger


def validate_date_format(date_str: str) -> bool:
    """
    验证日期格式是否正确
    Args:
        date_str: 日期字符串，支持 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式
    Returns:
        bool: 格式是否正确
    """
    try:
        # 尝试解析 YYYY-MM-DD HH:MM:SS 格式
        if len(date_str) == 19:
            datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
            return True
        # 尝试解析 YYYY-MM-DD 格式
        elif len(date_str) == 10:
            datetime.strptime(date_str, "%Y-%m-%d")
            return True
        else:
            return False
    except ValueError:
        return False


def convert_date_to_timestamp(date_str: str) -> int:
    """
    将日期字符串转换为Unix时间戳
    Args:
        date_str: 日期字符串，支持 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式
    Returns:
        int: Unix时间戳（秒）
    """
    try:
        if len(date_str) == 19:
            dt = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
        elif len(date_str) == 10:
            dt = datetime.strptime(date_str, "%Y-%m-%d")
        else:
            raise ValueError("Invalid date format")

        return int(dt.timestamp())
    except ValueError as e:
        logger.error(f"日期格式转换失败: {e}")
        raise


def convert_date_to_date_key(date_str: str) -> int:
    """
    将日期字符串转换为date_key格式（YYYYMMDD）
    Args:
        date_str: 日期字符串，支持 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式
    Returns:
        int: date_key格式的整数
    """
    try:
        if len(date_str) == 19:
            dt = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
        elif len(date_str) == 10:
            dt = datetime.strptime(date_str, "%Y-%m-%d")
        else:
            raise ValueError("Invalid date format")

        return int(dt.strftime("%Y%m%d"))
    except ValueError as e:
        logger.error(f"日期格式转换失败: {e}")
        raise


def get_day_range_timestamps(date_str: str) -> Tuple[int, int]:
    """
    获取指定日期当天的开始和结束时间戳
    Args:
        date_str: 日期字符串，格式为 YYYY-MM-DD
    Returns:
        Tuple[int, int]: (开始时间戳, 结束时间戳)
    """
    try:
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        start_time = date_obj.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = date_obj.replace(hour=23, minute=59, second=59, microsecond=999999)

        return int(start_time.timestamp()), int(end_time.timestamp())
    except ValueError as e:
        logger.error(f"日期范围计算失败: {e}")
        raise


async def query_overview_stat_by_uid(uid):
    """
    查询指定用户的概览统计数据
    Args:
        uid: 用户ID
    Returns:
        tuple: (统计数据列表, 数据条数)
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM overview_stat_table
                WHERE uid = $1
                ORDER BY create_time DESC
            """
            results = await conn.fetch(query, str(uid))
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询概览统计数据时发生未知错误: {e}")
        return [], 0


async def query_overview_stat_by_date_range(uid: str, start_date: str, end_date: str, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按时间范围查询指定用户的概览统计数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (统计数据列表, 数据条数)

    Example:
        results, count = await query_overview_stat_by_date_range(
            "123456789", "2022-01-01", "2022-01-31"
        )
    """
    try:
        
        if not validate_date_format(start_date) or not validate_date_format(end_date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式")
            return [], 0

        
        start_ts = convert_date_to_timestamp(start_date)
        end_ts = convert_date_to_timestamp(end_date)

        
        if start_ts > end_ts:
            logger.error("开始时间不能大于结束时间")
            return [], 0

        async with get_connection() as conn:
            query = """
                SELECT * FROM overview_stat_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                ORDER BY create_time DESC
                LIMIT $4 OFFSET $5
            """
            results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

            
            count_query = """
                SELECT COUNT(*) FROM overview_stat_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
            """
            total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按时间范围查询概览统计数据时发生未知错误: {e}")
        return [], 0


async def query_overview_stat_by_date(uid: str, date: str, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按特定日期查询指定用户的概览统计数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (统计数据列表, 数据条数)

    Example:
        results, count = await query_overview_stat_by_date("123456789", "2022-01-15")
    """
    try:
        
        if not validate_date_format(date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 格式")
            return [], 0

        # 获取当天的开始和结束时间戳
        start_ts, end_ts = get_day_range_timestamps(date)

        async with get_connection() as conn:
            query = """
                SELECT * FROM overview_stat_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                ORDER BY create_time DESC
                LIMIT $4 OFFSET $5
            """
            results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

            
            count_query = """
                SELECT COUNT(*) FROM overview_stat_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
            """
            total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按日期查询概览统计数据时发生未知错误: {e}")
        return [], 0


async def query_overview_stat_by_uid_and_timespan(uid, start_ts, end_ts):
    """
    查询指定用户在时间范围内的概览统计数据
    Args:
        uid: 用户ID
        start_ts: 开始时间戳(秒)
        end_ts: 结束时间戳(秒)
    Returns:
        tuple: (统计数据列表, 数据条数)
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM overview_stat_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                ORDER BY create_time DESC
            """
            results = await conn.fetch(query, str(uid), start_ts, end_ts)
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询概览统计数据时发生未知错误: {e}")
        return [], 0


async def query_latest_overview_stat_by_uid(uid):
    """
    查询指定用户的最新概览统计数据
    Args:
        uid: 用户ID
    Returns:
        dict: 最新统计数据记录
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM overview_stat_table
                WHERE uid = $1
                ORDER BY create_time DESC
                LIMIT 1
            """
            result = await conn.fetchrow(query, str(uid))
            return result

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return None
    except Exception as e:
        logger.error(f"查询最新概览统计数据时发生未知错误: {e}")
        return None


async def query_attention_analyze_by_uid(uid):
    """
    查询指定用户的涨粉分析数据
    Args:
        uid: 用户ID
    Returns:
        tuple: (分析数据列表, 数据条数)
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM attention_analyze_table
                WHERE uid = $1
                ORDER BY create_time DESC
            """
            results = await conn.fetch(query, str(uid))
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询涨粉分析数据时发生未知错误: {e}")
        return [], 0


async def query_latest_attention_analyze_by_uid(uid):
    """
    查询指定用户的最新涨粉分析数据
    Args:
        uid: 用户ID
    Returns:
        dict: 最新分析数据记录
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM attention_analyze_table
                WHERE uid = $1
                ORDER BY create_time DESC
                LIMIT 1
            """
            result = await conn.fetchrow(query, str(uid))
            return result

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return None
    except Exception as e:
        logger.error(f"查询最新涨粉分析数据时发生未知错误: {e}")
        return None


async def query_attention_analyze_by_date_range(uid: str, start_date: str, end_date: str, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按时间范围查询指定用户的涨粉分析数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (分析数据列表, 数据条数)

    Example:
        results, count = await query_attention_analyze_by_date_range(
            "123456789", "2022-01-01", "2022-01-31"
        )
    """
    try:
        
        if not validate_date_format(start_date) or not validate_date_format(end_date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式")
            return [], 0

        
        start_ts = convert_date_to_timestamp(start_date)
        end_ts = convert_date_to_timestamp(end_date)

        
        if start_ts > end_ts:
            logger.error("开始时间不能大于结束时间")
            return [], 0

        async with get_connection() as conn:
            query = """
                SELECT * FROM attention_analyze_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                ORDER BY create_time DESC
                LIMIT $4 OFFSET $5
            """
            results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

            
            count_query = """
                SELECT COUNT(*) FROM attention_analyze_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
            """
            total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按时间范围查询涨粉分析数据时发生未知错误: {e}")
        return [], 0


async def query_attention_analyze_by_date(uid: str, date: str, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按特定日期查询指定用户的涨粉分析数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (分析数据列表, 数据条数)

    Example:
        results, count = await query_attention_analyze_by_date("123456789", "2022-01-15")
    """
    try:
        
        if not validate_date_format(date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 格式")
            return [], 0

        # 获取当天的开始和结束时间戳
        start_ts, end_ts = get_day_range_timestamps(date)

        async with get_connection() as conn:
            query = """
                SELECT * FROM attention_analyze_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                ORDER BY create_time DESC
                LIMIT $4 OFFSET $5
            """
            results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

            
            count_query = """
                SELECT COUNT(*) FROM attention_analyze_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
            """
            total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按日期查询涨粉分析数据时发生未知错误: {e}")
        return [], 0


async def query_archive_analyze_by_uid_and_period(uid, period=0):
    """
    查询指定用户和周期的播放分析数据
    Args:
        uid: 用户ID
        period: 分析周期
    Returns:
        tuple: (分析数据列表, 数据条数)
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM archive_analyze_table
                WHERE uid = $1 AND period = $2
                ORDER BY create_time DESC
            """
            results = await conn.fetch(query, str(uid), period)
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询播放分析数据时发生未知错误: {e}")
        return [], 0


async def query_archive_analyze_by_date_range(uid: str, start_date: str, end_date: str, period: Optional[int] = None, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按时间范围查询指定用户的播放分析数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        period: 分析周期过滤（可选）
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (分析数据列表, 数据条数)

    Example:
        results, count = await query_archive_analyze_by_date_range(
            "123456789", "2022-01-01", "2022-01-31", period=7
        )
    """
    try:
        
        if not validate_date_format(start_date) or not validate_date_format(end_date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式")
            return [], 0

        start_ts = convert_date_to_timestamp(start_date)
        end_ts = convert_date_to_timestamp(end_date)

        if start_ts > end_ts:
            logger.error("开始时间不能大于结束时间")
            return [], 0

        async with get_connection() as conn:
            if period is not None:
                query = """
                    SELECT * FROM archive_analyze_table
                    WHERE uid = $1 AND period = $2 AND create_time BETWEEN $3 AND $4
                    ORDER BY create_time DESC
                    LIMIT $5 OFFSET $6
                """
                results = await conn.fetch(query, str(uid), period, start_ts, end_ts, limit, offset)

                count_query = """
                    SELECT COUNT(*) FROM archive_analyze_table
                    WHERE uid = $1 AND period = $2 AND create_time BETWEEN $3 AND $4
                """
                total_count = await conn.fetchval(count_query, str(uid), period, start_ts, end_ts)
            else:
                query = """
                    SELECT * FROM archive_analyze_table
                    WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                    ORDER BY create_time DESC
                    LIMIT $4 OFFSET $5
                """
                results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

                count_query = """
                    SELECT COUNT(*) FROM archive_analyze_table
                    WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                """
                total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按时间范围查询播放分析数据时发生未知错误: {e}")
        return [], 0


async def query_archive_analyze_by_date(uid: str, date: str, period: Optional[int] = None, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按特定日期查询指定用户的播放分析数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        period: 分析周期过滤（可选）
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (分析数据列表, 数据条数)

    Example:
        results, count = await query_archive_analyze_by_date("123456789", "2022-01-15", period=7)
    """
    try:
        
        if not validate_date_format(date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 格式")
            return [], 0

        # 获取当天的开始和结束时间戳
        start_ts, end_ts = get_day_range_timestamps(date)

        async with get_connection() as conn:
            if period is not None:
                query = """
                    SELECT * FROM archive_analyze_table
                    WHERE uid = $1 AND period = $2 AND create_time BETWEEN $3 AND $4
                    ORDER BY create_time DESC
                    LIMIT $5 OFFSET $6
                """
                results = await conn.fetch(query, str(uid), period, start_ts, end_ts, limit, offset)

                count_query = """
                    SELECT COUNT(*) FROM archive_analyze_table
                    WHERE uid = $1 AND period = $2 AND create_time BETWEEN $3 AND $4
                """
                total_count = await conn.fetchval(count_query, str(uid), period, start_ts, end_ts)
            else:
                query = """
                    SELECT * FROM archive_analyze_table
                    WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                    ORDER BY create_time DESC
                    LIMIT $4 OFFSET $5
                """
                results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

                count_query = """
                    SELECT COUNT(*) FROM archive_analyze_table
                    WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                """
                total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按日期查询播放分析数据时发生未知错误: {e}")
        return [], 0


async def query_fan_graph_by_uid(uid):
    """
    查询指定用户的粉丝图表数据
    Args:
        uid: 用户ID
    Returns:
        tuple: (图表数据列表, 数据条数)
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM fan_graph_table
                WHERE uid = $1
                ORDER BY create_time DESC
            """
            results = await conn.fetch(query, str(uid))
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询粉丝图表数据时发生未知错误: {e}")
        return [], 0


async def query_latest_fan_graph_by_uid(uid):
    """
    查询指定用户的最新粉丝图表数据
    Args:
        uid: 用户ID
    Returns:
        dict: 最新图表数据记录
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM fan_graph_table
                WHERE uid = $1
                ORDER BY create_time DESC
                LIMIT 1
            """
            result = await conn.fetchrow(query, str(uid))
            return result

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return None
    except Exception as e:
        logger.error(f"查询最新粉丝图表数据时发生未知错误: {e}")
        return None


async def query_fan_graph_by_date_range(uid: str, start_date: str, end_date: str, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按时间范围查询指定用户的粉丝图表数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (图表数据列表, 数据条数)

    Example:
        results, count = await query_fan_graph_by_date_range(
            "123456789", "2022-01-01", "2022-01-31"
        )
    """
    try:
        
        if not validate_date_format(start_date) or not validate_date_format(end_date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式")
            return [], 0

        
        start_ts = convert_date_to_timestamp(start_date)
        end_ts = convert_date_to_timestamp(end_date)

        
        if start_ts > end_ts:
            logger.error("开始时间不能大于结束时间")
            return [], 0

        async with get_connection() as conn:
            query = """
                SELECT * FROM fan_graph_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                ORDER BY create_time DESC
                LIMIT $4 OFFSET $5
            """
            results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

            
            count_query = """
                SELECT COUNT(*) FROM fan_graph_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
            """
            total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按时间范围查询粉丝图表数据时发生未知错误: {e}")
        return [], 0


async def query_fan_graph_by_date(uid: str, date: str, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按特定日期查询指定用户的粉丝图表数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (图表数据列表, 数据条数)

    Example:
        results, count = await query_fan_graph_by_date("123456789", "2022-01-15")
    """
    try:
        
        if not validate_date_format(date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 格式")
            return [], 0

        # 获取当天的开始和结束时间戳
        start_ts, end_ts = get_day_range_timestamps(date)

        async with get_connection() as conn:
            query = """
                SELECT * FROM fan_graph_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                ORDER BY create_time DESC
                LIMIT $4 OFFSET $5
            """
            results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

            
            count_query = """
                SELECT COUNT(*) FROM fan_graph_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
            """
            total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按日期查询粉丝图表数据时发生未知错误: {e}")
        return [], 0


async def query_fan_overview_by_uid(uid):
    """
    查询指定用户的粉丝概览数据
    Args:
        uid: 用户ID
    Returns:
        tuple: (概览数据列表, 数据条数)
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM fan_overview_table
                WHERE uid = $1
                ORDER BY create_time DESC
            """
            results = await conn.fetch(query, str(uid))
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询粉丝概览数据时发生未知错误: {e}")
        return [], 0


async def query_latest_fan_overview_by_uid(uid):
    """
    查询指定用户的最新粉丝概览数据
    Args:
        uid: 用户ID
    Returns:
        dict: 最新概览数据记录
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM fan_overview_table
                WHERE uid = $1
                ORDER BY create_time DESC
                LIMIT 1
            """
            result = await conn.fetchrow(query, str(uid))
            return result

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return None
    except Exception as e:
        logger.error(f"查询最新粉丝概览数据时发生未知错误: {e}")
        return None


async def query_fan_overview_by_date_range(uid: str, start_date: str, end_date: str, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按时间范围查询指定用户的粉丝概览数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (概览数据列表, 数据条数)

    Example:
        results, count = await query_fan_overview_by_date_range(
            "123456789", "2022-01-01", "2022-01-31"
        )
    """
    try:
        
        if not validate_date_format(start_date) or not validate_date_format(end_date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式")
            return [], 0

        
        start_ts = convert_date_to_timestamp(start_date)
        end_ts = convert_date_to_timestamp(end_date)

        
        if start_ts > end_ts:
            logger.error("开始时间不能大于结束时间")
            return [], 0

        async with get_connection() as conn:
            query = """
                SELECT * FROM fan_overview_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                ORDER BY create_time DESC
                LIMIT $4 OFFSET $5
            """
            results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

            
            count_query = """
                SELECT COUNT(*) FROM fan_overview_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
            """
            total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按时间范围查询粉丝概览数据时发生未知错误: {e}")
        return [], 0


async def query_fan_overview_by_date(uid: str, date: str, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按特定日期查询指定用户的粉丝概览数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (概览数据列表, 数据条数)

    Example:
        results, count = await query_fan_overview_by_date("123456789", "2022-01-15")
    """
    try:
        
        if not validate_date_format(date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 格式")
            return [], 0

        # 获取当天的开始和结束时间戳
        start_ts, end_ts = get_day_range_timestamps(date)

        async with get_connection() as conn:
            query = """
                SELECT * FROM fan_overview_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                ORDER BY create_time DESC
                LIMIT $4 OFFSET $5
            """
            results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

            
            count_query = """
                SELECT COUNT(*) FROM fan_overview_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
            """
            total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按日期查询粉丝概览数据时发生未知错误: {e}")
        return [], 0


async def query_video_compare_by_uid(uid, limit=100):
    """
    查询指定用户的视频对比数据
    Args:
        uid: 用户ID
        limit: 限制返回数量
    Returns:
        tuple: (视频对比数据列表, 数据条数)
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM video_compare_table
                WHERE uid = $1
                ORDER BY create_time DESC
                LIMIT $2
            """
            results = await conn.fetch(query, str(uid), limit)
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询视频对比数据时发生未知错误: {e}")
        return [], 0


async def query_video_compare_by_uid_and_bvid(uid, bvid):
    """
    查询指定用户和视频BV号的对比数据
    Args:
        uid: 用户ID
        bvid: 视频BV号
    Returns:
        dict: 视频对比数据记录
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM video_compare_table
                WHERE uid = $1 AND bvid = $2
                ORDER BY create_time DESC
                LIMIT 1
            """
            result = await conn.fetchrow(query, str(uid), bvid)
            return result

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return None
    except Exception as e:
        logger.error(f"查询视频对比数据时发生未知错误: {e}")
        return None


async def query_video_compare_by_date_range(uid: str, start_date: str, end_date: str, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按时间范围查询指定用户的视频对比数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (视频对比数据列表, 数据条数)

    Example:
        results, count = await query_video_compare_by_date_range(
            "123456789", "2022-01-01", "2022-01-31"
        )
    """
    try:
        
        if not validate_date_format(start_date) or not validate_date_format(end_date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式")
            return [], 0

        
        start_ts = convert_date_to_timestamp(start_date)
        end_ts = convert_date_to_timestamp(end_date)

        
        if start_ts > end_ts:
            logger.error("开始时间不能大于结束时间")
            return [], 0

        async with get_connection() as conn:
            query = """
                SELECT * FROM video_compare_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                ORDER BY create_time DESC
                LIMIT $4 OFFSET $5
            """
            results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

            
            count_query = """
                SELECT COUNT(*) FROM video_compare_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
            """
            total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按时间范围查询视频对比数据时发生未知错误: {e}")
        return [], 0


async def query_video_compare_by_date(uid: str, date: str, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按特定日期查询指定用户的视频对比数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (视频对比数据列表, 数据条数)

    Example:
        results, count = await query_video_compare_by_date("123456789", "2022-01-15")
    """
    try:
        
        if not validate_date_format(date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 格式")
            return [], 0

        # 获取当天的开始和结束时间戳
        start_ts, end_ts = get_day_range_timestamps(date)

        async with get_connection() as conn:
            query = """
                SELECT * FROM video_compare_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                ORDER BY create_time DESC
                LIMIT $4 OFFSET $5
            """
            results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

            
            count_query = """
                SELECT COUNT(*) FROM video_compare_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
            """
            total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按日期查询视频对比数据时发生未知错误: {e}")
        return [], 0


async def query_video_pandect_by_uid_and_data_type(uid, data_type_column):
    """
    查询指定用户和数据类型的视频趋势数据
    Args:
        uid: 用户ID
        data_type_column: 数据类型列名
    Returns:
        tuple: (趋势数据列表, 数据条数)
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM video_pandect_table
                WHERE uid = $1 AND data_type_column = $2
                ORDER BY date_key DESC
            """
            results = await conn.fetch(query, str(uid), data_type_column)
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询视频趋势数据时发生未知错误: {e}")
        return [], 0


async def query_video_pandect_by_uid_and_timespan(uid, start_date_key, end_date_key):
    """
    查询指定用户在时间范围内的视频趋势数据
    Args:
        uid: 用户ID
        start_date_key: 开始日期键
        end_date_key: 结束日期键
    Returns:
        tuple: (趋势数据列表, 数据条数)
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM video_pandect_table
                WHERE uid = $1 AND date_key BETWEEN $2 AND $3
                ORDER BY date_key DESC, data_type_column
            """
            results = await conn.fetch(query, str(uid), start_date_key, end_date_key)
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询视频趋势数据时发生未知错误: {e}")
        return [], 0


async def query_video_pandect_by_date_range(uid: str, start_date: str, end_date: str, data_type_column: Optional[str] = None, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按时间范围查询指定用户的视频趋势数据

    Args:
        uid: 用户ID
        start_date: 开始日期，格式：YYYY-MM-DD
        end_date: 结束日期，格式：YYYY-MM-DD
        data_type_column: 数据类型过滤（可选）
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (趋势数据列表, 数据条数)

    Example:
        results, count = await query_video_pandect_by_date_range(
            "123456789", "2022-01-01", "2022-01-31", "view"
        )
    """
    try:
        if not validate_date_format(start_date) or not validate_date_format(end_date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 格式")
            return [], 0

        # 转换为date_key格式
        start_date_key = convert_date_to_date_key(start_date)
        end_date_key = convert_date_to_date_key(end_date)

        
        if start_date_key > end_date_key:
            logger.error("开始日期不能大于结束日期")
            return [], 0

        async with get_connection() as conn:
            if data_type_column is not None:
                query = """
                    SELECT * FROM video_pandect_table
                    WHERE uid = $1 AND data_type_column = $2 AND date_key BETWEEN $3 AND $4
                    ORDER BY date_key DESC
                    LIMIT $5 OFFSET $6
                """
                results = await conn.fetch(query, str(uid), data_type_column, start_date_key, end_date_key, limit, offset)

                count_query = """
                    SELECT COUNT(*) FROM video_pandect_table
                    WHERE uid = $1 AND data_type_column = $2 AND date_key BETWEEN $3 AND $4
                """
                total_count = await conn.fetchval(count_query, str(uid), data_type_column, start_date_key, end_date_key)
            else:
                query = """
                    SELECT * FROM video_pandect_table
                    WHERE uid = $1 AND date_key BETWEEN $2 AND $3
                    ORDER BY date_key DESC, data_type_column
                    LIMIT $4 OFFSET $5
                """
                results = await conn.fetch(query, str(uid), start_date_key, end_date_key, limit, offset)

                count_query = """
                    SELECT COUNT(*) FROM video_pandect_table
                    WHERE uid = $1 AND date_key BETWEEN $2 AND $3
                """
                total_count = await conn.fetchval(count_query, str(uid), start_date_key, end_date_key)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按时间范围查询视频趋势数据时发生未知错误: {e}")
        return [], 0


async def query_video_pandect_by_date(uid: str, date: str, data_type_column: Optional[str] = None, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按特定日期查询指定用户的视频趋势数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        data_type_column: 数据类型过滤（可选）
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (趋势数据列表, 数据条数)

    Example:
        results, count = await query_video_pandect_by_date("123456789", "2022-01-15", "view")
    """
    try:
        
        if not validate_date_format(date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 格式")
            return [], 0

        # 转换为date_key格式
        date_key = convert_date_to_date_key(date)

        async with get_connection() as conn:
            if data_type_column is not None:
                query = """
                    SELECT * FROM video_pandect_table
                    WHERE uid = $1 AND data_type_column = $2 AND date_key = $3
                    ORDER BY date_key DESC
                    LIMIT $4 OFFSET $5
                """
                results = await conn.fetch(query, str(uid), data_type_column, date_key, limit, offset)

                count_query = """
                    SELECT COUNT(*) FROM video_pandect_table
                    WHERE uid = $1 AND data_type_column = $2 AND date_key = $3
                """
                total_count = await conn.fetchval(count_query, str(uid), data_type_column, date_key)
            else:
                query = """
                    SELECT * FROM video_pandect_table
                    WHERE uid = $1 AND date_key = $2
                    ORDER BY data_type_column
                    LIMIT $3 OFFSET $4
                """
                results = await conn.fetch(query, str(uid), date_key, limit, offset)

                count_query = """
                    SELECT COUNT(*) FROM video_pandect_table
                    WHERE uid = $1 AND date_key = $2
                """
                total_count = await conn.fetchval(count_query, str(uid), date_key)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按日期查询视频趋势数据时发生未知错误: {e}")
        return [], 0


async def query_video_survey_by_uid_and_data_type(uid, data_type):
    """
    查询指定用户和数据类型的视频调查数据
    Args:
        uid: 用户ID
        data_type: 数据类型
    Returns:
        tuple: (调查数据列表, 数据条数)
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM video_survey_table
                WHERE uid = $1 AND data_type = $2
                ORDER BY create_time DESC
            """
            results = await conn.fetch(query, str(uid), data_type)
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询视频调查数据时发生未知错误: {e}")
        return [], 0


async def query_latest_video_survey_by_uid_and_data_type(uid, data_type):
    """
    查询指定用户和数据类型的最新视频调查数据
    Args:
        uid: 用户ID
        data_type: 数据类型
    Returns:
        dict: 最新调查数据记录
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM video_survey_table
                WHERE uid = $1 AND data_type = $2
                ORDER BY create_time DESC
                LIMIT 1
            """
            result = await conn.fetchrow(query, str(uid), data_type)
            return result

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return None
    except Exception as e:
        logger.error(f"查询最新视频调查数据时发生未知错误: {e}")
        return None


async def query_video_survey_by_date_range(uid: str, start_date: str, end_date: str, data_type: Optional[int] = None, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按时间范围查询指定用户的视频调查数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        data_type: 数据类型过滤（可选）
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (调查数据列表, 数据条数)

    Example:
        results, count = await query_video_survey_by_date_range(
            "123456789", "2022-01-01", "2022-01-31", data_type=1
        )
    """
    try:
        
        if not validate_date_format(start_date) or not validate_date_format(end_date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式")
            return [], 0

        
        start_ts = convert_date_to_timestamp(start_date)
        end_ts = convert_date_to_timestamp(end_date)

        
        if start_ts > end_ts:
            logger.error("开始时间不能大于结束时间")
            return [], 0

        async with get_connection() as conn:
            if data_type is not None:
                query = """
                    SELECT * FROM video_survey_table
                    WHERE uid = $1 AND data_type = $2 AND create_time BETWEEN $3 AND $4
                    ORDER BY create_time DESC
                    LIMIT $5 OFFSET $6
                """
                results = await conn.fetch(query, str(uid), data_type, start_ts, end_ts, limit, offset)

                count_query = """
                    SELECT COUNT(*) FROM video_survey_table
                    WHERE uid = $1 AND data_type = $2 AND create_time BETWEEN $3 AND $4
                """
                total_count = await conn.fetchval(count_query, str(uid), data_type, start_ts, end_ts)
            else:
                query = """
                    SELECT * FROM video_survey_table
                    WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                    ORDER BY create_time DESC
                    LIMIT $4 OFFSET $5
                """
                results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

                count_query = """
                    SELECT COUNT(*) FROM video_survey_table
                    WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                """
                total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按时间范围查询视频调查数据时发生未知错误: {e}")
        return [], 0


async def query_video_survey_by_date(uid: str, date: str, data_type: Optional[int] = None, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按特定日期查询指定用户的视频调查数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        data_type: 数据类型过滤（可选）
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (调查数据列表, 数据条数)

    Example:
        results, count = await query_video_survey_by_date("123456789", "2022-01-15", data_type=1)
    """
    try:
        
        if not validate_date_format(date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 格式")
            return [], 0

        # 获取当天的开始和结束时间戳
        start_ts, end_ts = get_day_range_timestamps(date)

        async with get_connection() as conn:
            if data_type is not None:
                query = """
                    SELECT * FROM video_survey_table
                    WHERE uid = $1 AND data_type = $2 AND create_time BETWEEN $3 AND $4
                    ORDER BY create_time DESC
                    LIMIT $5 OFFSET $6
                """
                results = await conn.fetch(query, str(uid), data_type, start_ts, end_ts, limit, offset)

                count_query = """
                    SELECT COUNT(*) FROM video_survey_table
                    WHERE uid = $1 AND data_type = $2 AND create_time BETWEEN $3 AND $4
                """
                total_count = await conn.fetchval(count_query, str(uid), data_type, start_ts, end_ts)
            else:
                query = """
                    SELECT * FROM video_survey_table
                    WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                    ORDER BY create_time DESC
                    LIMIT $4 OFFSET $5
                """
                results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

                count_query = """
                    SELECT COUNT(*) FROM video_survey_table
                    WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                """
                total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按日期查询视频调查数据时发生未知错误: {e}")
        return [], 0


async def query_video_source_by_uid(uid):
    """
    查询指定用户的视频来源数据
    Args:
        uid: 用户ID
    Returns:
        tuple: (来源数据列表, 数据条数)
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM video_source_table
                WHERE uid = $1
                ORDER BY create_time DESC
            """
            results = await conn.fetch(query, str(uid))
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询视频来源数据时发生未知错误: {e}")
        return [], 0


async def query_latest_video_source_by_uid(uid):
    """
    查询指定用户的最新视频来源数据
    Args:
        uid: 用户ID
    Returns:
        dict: 最新来源数据记录
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM video_source_table
                WHERE uid = $1
                ORDER BY create_time DESC
                LIMIT 1
            """
            result = await conn.fetchrow(query, str(uid))
            return result

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return None
    except Exception as e:
        logger.error(f"查询最新视频来源数据时发生未知错误: {e}")
        return None


async def query_video_source_by_date_range(uid: str, start_date: str, end_date: str, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按时间范围查询指定用户的视频来源数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (来源数据列表, 数据条数)

    Example:
        results, count = await query_video_source_by_date_range(
            "123456789", "2022-01-01", "2022-01-31"
        )
    """
    try:
        
        if not validate_date_format(start_date) or not validate_date_format(end_date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式")
            return [], 0

        
        start_ts = convert_date_to_timestamp(start_date)
        end_ts = convert_date_to_timestamp(end_date)

        
        if start_ts > end_ts:
            logger.error("开始时间不能大于结束时间")
            return [], 0

        async with get_connection() as conn:
            query = """
                SELECT * FROM video_source_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                ORDER BY create_time DESC
                LIMIT $4 OFFSET $5
            """
            results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

            
            count_query = """
                SELECT COUNT(*) FROM video_source_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
            """
            total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按时间范围查询视频来源数据时发生未知错误: {e}")
        return [], 0


async def query_video_source_by_date(uid: str, date: str, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按特定日期查询指定用户的视频来源数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (来源数据列表, 数据条数)

    Example:
        results, count = await query_video_source_by_date("123456789", "2022-01-15")
    """
    try:
        
        if not validate_date_format(date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 格式")
            return [], 0

        # 获取当天的开始和结束时间戳
        start_ts, end_ts = get_day_range_timestamps(date)

        async with get_connection() as conn:
            query = """
                SELECT * FROM video_source_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                ORDER BY create_time DESC
                LIMIT $4 OFFSET $5
            """
            results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

            
            count_query = """
                SELECT COUNT(*) FROM video_source_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
            """
            total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按日期查询视频来源数据时发生未知错误: {e}")
        return [], 0


async def query_video_view_data_by_uid(uid):
    """
    查询指定用户的视频观看数据
    Args:
        uid: 用户ID
    Returns:
        tuple: (观看数据列表, 数据条数)
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM video_view_data_table
                WHERE uid = $1
                ORDER BY create_time DESC
            """
            results = await conn.fetch(query, str(uid))
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询视频观看数据时发生未知错误: {e}")
        return [], 0


async def query_latest_video_view_data_by_uid(uid):
    """
    查询指定用户的最新视频观看数据
    Args:
        uid: 用户ID
    Returns:
        dict: 最新观看数据记录
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM video_view_data_table
                WHERE uid = $1
                ORDER BY create_time DESC
                LIMIT 1
            """
            result = await conn.fetchrow(query, str(uid))
            return result

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return None
    except Exception as e:
        logger.error(f"查询最新视频观看数据时发生未知错误: {e}")
        return None


async def query_video_view_data_by_date_range(uid: str, start_date: str, end_date: str, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按时间范围查询指定用户的视频观看数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (观看数据列表, 数据条数)

    Example:
        results, count = await query_video_view_data_by_date_range(
            "123456789", "2022-01-01", "2022-01-31"
        )
    """
    try:
        
        if not validate_date_format(start_date) or not validate_date_format(end_date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式")
            return [], 0

        
        start_ts = convert_date_to_timestamp(start_date)
        end_ts = convert_date_to_timestamp(end_date)

        
        if start_ts > end_ts:
            logger.error("开始时间不能大于结束时间")
            return [], 0

        async with get_connection() as conn:
            query = """
                SELECT * FROM video_view_data_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                ORDER BY create_time DESC
                LIMIT $4 OFFSET $5
            """
            results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

            
            count_query = """
                SELECT COUNT(*) FROM video_view_data_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
            """
            total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按时间范围查询视频观看数据时发生未知错误: {e}")
        return [], 0


async def query_video_view_data_by_date(uid: str, date: str, limit: int = 1000, offset: int = 0) -> Tuple[List, int]:
    """
    按特定日期查询指定用户的视频观看数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        Tuple[List, int]: (观看数据列表, 数据条数)

    Example:
        results, count = await query_video_view_data_by_date("123456789", "2022-01-15")
    """
    try:
        
        if not validate_date_format(date):
            logger.error("日期格式错误，请使用 YYYY-MM-DD 格式")
            return [], 0

        # 获取当天的开始和结束时间戳
        start_ts, end_ts = get_day_range_timestamps(date)

        async with get_connection() as conn:
            query = """
                SELECT * FROM video_view_data_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
                ORDER BY create_time DESC
                LIMIT $4 OFFSET $5
            """
            results = await conn.fetch(query, str(uid), start_ts, end_ts, limit, offset)

            
            count_query = """
                SELECT COUNT(*) FROM video_view_data_table
                WHERE uid = $1 AND create_time BETWEEN $2 AND $3
            """
            total_count = await conn.fetchval(count_query, str(uid), start_ts, end_ts)

            return results, total_count

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"按日期查询视频观看数据时发生未知错误: {e}")
        return [], 0


async def query_creator_data_summary_by_uid(uid):
    """
    查询指定用户的创作者数据汇总
    Args:
        uid: 用户ID
    Returns:
        dict: 包含各类数据汇总的字典
    """
    try:
        summary = {
            "uid": uid,
            "overview_stat": None,
            "attention_analyze": None,
            "fan_overview": None,
            "video_source": None,
            "video_view_data": None,
            "video_count": 0,
            "last_update_time": None
        }

        # 获取最新的概览统计数据
        overview_stat = await query_latest_overview_stat_by_uid(uid)
        if overview_stat:
            summary["overview_stat"] = dict(overview_stat)
            summary["last_update_time"] = overview_stat["update_time"]

        # 获取最新的涨粉分析数据
        attention_analyze = await query_latest_attention_analyze_by_uid(uid)
        if attention_analyze:
            summary["attention_analyze"] = dict(attention_analyze)

        # 获取最新的粉丝概览数据
        fan_overview = await query_latest_fan_overview_by_uid(uid)
        if fan_overview:
            summary["fan_overview"] = dict(fan_overview)

        # 获取最新的视频来源数据
        video_source = await query_latest_video_source_by_uid(uid)
        if video_source:
            summary["video_source"] = dict(video_source)

        # 获取最新的视频观看数据
        video_view_data = await query_latest_video_view_data_by_uid(uid)
        if video_view_data:
            summary["video_view_data"] = dict(video_view_data)

        # 获取视频数量
        _, video_count = await query_video_compare_by_uid(uid, limit=1000)
        summary["video_count"] = video_count

        return summary

    except Exception as e:
        logger.error(f"查询创作者数据汇总时发生未知错误: {e}")
        return None


async def query_video_performance_trends_by_uid(uid, days=30):
    """
    查询指定用户的视频表现趋势
    Args:
        uid: 用户ID
        days: 查询天数
    Returns:
        dict: 包含各类趋势数据的字典
    """
    try:
        from datetime import datetime, timedelta

        # 计算时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        start_date_key = int(start_time.timestamp())
        end_date_key = int(end_time.timestamp())

        trends = {
            "uid": uid,
            "period_days": days,
            "play_trend": [],
            "dm_trend": [],
            "comment_trend": [],
            "like_trend": [],
            "coin_trend": [],
            "fav_trend": [],
            "share_trend": [],
            "elec_trend": []
        }

        # 查询各类数据趋势
        data_types = [
            ("play_total_inc", "play_trend"),
            ("dm_total_inc", "dm_trend"),
            ("comment_total_inc", "comment_trend"),
            ("like_total_inc", "like_trend"),
            ("coin_total_inc", "coin_trend"),
            ("fav_total_inc", "fav_trend"),
            ("share_total_inc", "share_trend"),
            ("elec_total_inc", "elec_trend")
        ]

        for data_type, trend_key in data_types:
            trend_data, _ = await query_video_pandect_by_uid_and_data_type(uid, data_type)
            # 过滤时间范围内的数据
            filtered_data = [
                record for record in trend_data
                if start_date_key <= record["date_key"] <= end_date_key
            ]
            trends[trend_key] = filtered_data

        return trends

    except Exception as e:
        logger.error(f"查询视频表现趋势时发生未知错误: {e}")
        return None
